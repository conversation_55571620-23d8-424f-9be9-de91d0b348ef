import { Validation } from "../common/validation";
import { ValidationRuleEnum } from "../enums/validation-rule";
import { checkAndAdjustValueByValidationRules } from "../functions/check-and-adjust-value";
import {
  orichiLimitGetQuantityProductInCart,
  orichiLimitGetQuantityVariantInCart
} from "../functions/get-quantity";
import { SELECTOR_ELEMENT } from "../main";
import { getVariantId } from "../utils/common";
import { parameter } from "../utils/parameter";

export const ValidationProduct = async (currentLimit, step = 0) => {
  const { getValue } = parameter();
  const currentProduct = window.orichiLimit.currentProduct;
  const quantityInCart = await orichiLimitGetQuantityProductInCart(currentProduct.id);
  let currentQuantity = parseInt($(SELECTOR_ELEMENT.QUANTITY_INPUT).val()) || 1;

  if (getValue("PRODUCT_CHECK_QUANTITY_AND_ADJUST")) {
    // check and adjust value by ValidationRules
    currentQuantity = checkAndAdjustValueByValidationRules(currentLimit, currentQuantity);
  }

  const totalQuantity = currentQuantity + parseInt(quantityInCart) + step;

  return Validation({ currentLimit, totalQuantity });
};

export const ValidationVariant = async (currentLimit, step = 0) => {
  const { getValue } = parameter();
  let variantElement = $(SELECTOR_ELEMENT.ORICHI_VARIANT_SELECTOR);

  // Get variant ID from currentLimit object first, fallback to URL/selector
  let currentVariant;
  if (currentLimit && currentLimit.ShopifyObjects && currentLimit.ShopifyObjects.length > 0) {
    // Use variant ID from the limit object
    currentVariant = currentLimit.ShopifyObjects[0].Id;
    console.log("🚀 ValidationVariant - Using variant ID from currentLimit:", currentVariant);
  } else {
    // Fallback to URL/selector method
    if (getValue("PRODUCT_GET_VARIANT_ID_FROM_URL") === "true") {
      const urlVariantId = getVariantId("variant");
      const selectorVariantId = variantElement.val();
      currentVariant = urlVariantId || selectorVariantId;
      console.log(
        "🚀 ValidationVariant - URL mode fallback - urlVariantId:",
        urlVariantId,
        "selectorVariantId:",
        selectorVariantId,
        "final currentVariant:",
        currentVariant
      );
    } else {
      currentVariant = variantElement.val();
      console.log(
        "🚀 ValidationVariant - Selector mode fallback - currentVariant:",
        currentVariant
      );
    }
  }

  const quantityInCart = await orichiLimitGetQuantityVariantInCart(currentVariant);
  let currentQuantity = parseInt($(SELECTOR_ELEMENT.QUANTITY_INPUT).val()) || 1;

  if (getValue("PRODUCT_CHECK_QUANTITY_AND_ADJUST")) {
    // check and adjust value by ValidationRules
    currentQuantity = checkAndAdjustValueByValidationRules(currentLimit, currentQuantity);
  }

  const totalQuantity = currentQuantity + parseInt(quantityInCart) + step;

  return Validation({ currentLimit, totalQuantity });
};

export const firstRender = (currentLimit) => {
  const { getValue } = parameter();

  if (!currentLimit) {
    $(SELECTOR_ELEMENT.ORICHI_BUTTON_IN_FORM_CART)
      .not(SELECTOR_ELEMENT.ORICHI_BUTTON_PLUS_MINUS)
      .attr("disabled", false);
    $(".orichi-limit-error").remove();
    return;
  }

  // find rule by type
  const multiplesRule = currentLimit.ValidationRules.find(
    (rule) => rule.Type === ValidationRuleEnum.MULTIPLES
  );
  const minimumRule = currentLimit.ValidationRules.find(
    (rule) => rule.Type === ValidationRuleEnum.MINIMUM_QUANTITY
  );
  const specificRule = currentLimit.ValidationRules.find(
    (rule) => rule.Type === ValidationRuleEnum.SPECIFIC_QUANTITY
  );

  // handle case have both MULTIPLES and MINIMUM_QUANTITY
  if (multiplesRule && minimumRule && getValue("PRODUCT_SET_STEP_AND_MINIMUM_QUANTITY")) {
    const multiplesValue = parseInt(multiplesRule.Value);
    const minimumValue = parseInt(minimumRule.Value);
    // find first multiples value greater than or equal to minimumValue
    const initialValue = Math.ceil(minimumValue / multiplesValue) * multiplesValue;
    $(SELECTOR_ELEMENT.QUANTITY_INPUT).val(initialValue);
    $(SELECTOR_ELEMENT.QUANTITY_INPUT).attr("step", multiplesValue);
    $(SELECTOR_ELEMENT.QUANTITY_INPUT).attr("min", initialValue);
    return;
  }

  // handle case have both MINIMUM_QUANTITY and SPECIFIC_QUANTITY
  if (minimumRule && specificRule && getValue("PRODUCT_SET_VALUE_QUANTITY")) {
    const minimumValue = parseInt(minimumRule.Value);
    const specificValue = parseInt(specificRule.Value);
    // choose greater value between minimum and specific
    const value = Math.max(minimumValue, specificValue);
    $(SELECTOR_ELEMENT.QUANTITY_INPUT).val(value);
    return;
  }

  // handle case have only one rule
  currentLimit.ValidationRules.forEach((rule) => {
    const numericValue = parseInt(rule.Value);
    if (rule.Type === ValidationRuleEnum.MULTIPLES) {
      $(SELECTOR_ELEMENT.QUANTITY_INPUT).val(numericValue);
      $(SELECTOR_ELEMENT.QUANTITY_INPUT).attr("step", numericValue);
      $(SELECTOR_ELEMENT.QUANTITY_INPUT).attr("min", 0);
      return;
    }
    if (rule.Type === ValidationRuleEnum.MINIMUM_QUANTITY) {
      $(SELECTOR_ELEMENT.QUANTITY_INPUT).val(numericValue || 1);
      return;
    }
    if (rule.Type === ValidationRuleEnum.SPECIFIC_QUANTITY) {
      $(SELECTOR_ELEMENT.QUANTITY_INPUT).val(numericValue);
      return;
    }
  });
};
