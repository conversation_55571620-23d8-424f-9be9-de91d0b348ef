export const html = String.raw;

export const getMultiplesQuantity = (currentQuantity, value) => {
  return currentQuantity % value === 0;
};

export const wait = (time) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve();
    }, time);
  });
};

export const removeDuplicateErrors = (errors) => {
  return Array.from(new Set(errors.map(JSON.stringify))).map(JSON.parse);
};

export const getVariantId = (searchParams) => {
  const url = new URL(window.location.href);
  const variantId = url.searchParams.get(searchParams);
  return variantId ? parseInt(variantId, 10) : 0;
};
